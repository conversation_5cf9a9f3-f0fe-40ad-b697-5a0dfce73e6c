/*
  This file controls the video height in relation to the right side content
  in the PreJoin component. It ensures that in the 6-6 column layout (side-by-side),
  the video component height is always greater than the right side content height,
  while maintaining the current behavior in the 12-12 column layout (stacked).
*/

// Media query for medium and larger screens (side-by-side layout)
@media (min-width: 768px) {
  .video-height-control-row {
    // This ensures the row maintains proper alignment
    align-items: stretch !important;

    // Target the columns directly
    .video-column {
      display: flex;
      flex-direction: column;

      // Set minimum height to ensure it's always taller than right side
      min-height: 470px;

      // Ensure the video container takes full height
      .d-flex.flex-column {
        height: 100%;

        // Make the video container grow to fill available space
        .d-flex.justify-content-center {
          height: 100%;

          // Ensure the PreJoinAudioVideo component fills the space
          .video-prejoin-container {
            height: 100%;
            max-height: none;

            // Maintain aspect ratio while filling height
            .lk-video-container,
            .lk-camera-off-note {
              height: 100%;
            }
          }
        }
      }
    }

    // Second column (form container)
    .form-column {
      // Set max-height to ensure it's never taller than the video
      max-height: 100%;

      // Center content vertically
      .d-flex.flex-column {
        height: auto;
        max-height: 100%;

        // Ensure the container-parent-form doesn't exceed video height
        .container-parent-form {
          max-height: 100%;
          overflow-y: auto;
          padding: 1rem 0;

          // Ensure form grows from center
          .prejoin-form {
            transform-origin: center center;
          }
        }
      }
    }
  }
}

// Additional styles for larger screens to maintain proportions
@media (min-width: 992px) {
  .video-height-control-row {
    .video-column {
      min-height: 450px;

      .d-flex.flex-column {
        .d-flex.justify-content-center {
          .video-prejoin-container {
            aspect-ratio: 16/9;
            width: 100%;
            
            .lk-video-container,
            .lk-camera-off-note {
              aspect-ratio: 16/9;
            }
          }
        }
      }
    }
  }
}

@media (min-width: 1200px) {
  .video-height-control-row {
    .video-column {
      min-height: 500px;

      .d-flex.flex-column {
        .d-flex.justify-content-center {
          .video-prejoin-container {
            aspect-ratio: 16/9;
            width: 100%;
            
            .lk-video-container,
            .lk-camera-off-note {
              aspect-ratio: 16/9;
            }
          }
        }
      }
    }
  }
}

// For extra large screens
@media (min-width: 1400px) {
  .video-height-control-row {
    .video-column {
      min-height: 550px;

      .d-flex.flex-column {
        .d-flex.justify-content-center {
          .video-prejoin-container {
            aspect-ratio: 16/9;
            width: 100%;
            
            .lk-video-container,
            .lk-camera-off-note {
              aspect-ratio: 16/9;
            }
          }
        }
      }
    }
  }
}

// For mobile screens (stacked layout), maintain current behavior
@media (max-width: 767.98px) {
  .video-height-control-row {
    .video-column {
      width: 100%;
      margin-bottom: 1.5rem;

      .video-prejoin-container {
        // Use the existing aspect ratio from audioVideoPrejoin.scss
        aspect-ratio: 4/3;
        height: auto;
      }
    }

    .form-column {
      width: 100%;
    }
  }
}
