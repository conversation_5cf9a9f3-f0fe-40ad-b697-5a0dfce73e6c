/* eslint-disable */
import React, { useState, useEffect } from "react";
import { Dropdown, Spin, Empty } from "antd";
import "./MicDeviceDropdown.scss";

export default function MicDeviceDropdown({
  deviceId,
  setDeviceId,
  children,
  permissionsGranted = false
}) {
  const [devices, setDevices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [open, setOpen] = useState(false);

  useEffect(() => {
    const getDevices = async () => {
      setLoading(true);
      try {
        const mediaDevices = await navigator.mediaDevices.enumerateDevices();
        const audioInputs = mediaDevices.filter(device => device.kind === 'audioinput');
        setDevices(audioInputs);
      } catch (error) {
        console.error('Error getting media devices:', error);
        setDevices([]);
      } finally {
        setLoading(false);
      }
    };

    // Only fetch devices if permissions are granted
    if (permissionsGranted) {
      getDevices();
    } else {
      setLoading(false);
      setDevices([]);
    }
  }, [deviceId, permissionsGranted]);

  const truncateDeviceName = (name) => {
    const maxLength = window.innerWidth < 768 ? 20 : 30;
    return name.length > maxLength ? `${name.slice(0, maxLength)}...` : name;
  };

  let items = [];
  if (!permissionsGranted) {
    items = [{
      key: 'no-permission',
      label: <div style={{ textAlign: 'center', padding: 8 }}>
        <div>Grant microphone permission to see devices</div>
      </div>,
      disabled: true
    }];
  } else if (loading) {
    items = [{
      key: 'loading',
      label: <div style={{ textAlign: 'center', padding: 8 }}><Spin size="small" /></div>,
      disabled: true
    }];
  } else if (devices.length === 0) {
    items = [{
      key: 'empty',
      label: <div style={{ textAlign: 'center', padding: 8 }}><Empty description="No microphones found" image={Empty.PRESENTED_IMAGE_SIMPLE} /></div>,
      disabled: true
    }];
  } else {
    items = devices.map((device) => ({
      key: device.deviceId,
      label: (
        <div
          className={`mic-device-item ${device.deviceId === deviceId ? 'selected' : ''}`}
          onClick={() => {
            setDeviceId(device.deviceId);
          }}
          title={device.label}
          tabIndex={0}
          aria-label={truncateDeviceName(device.label || 'Default Microphone')}
          onKeyDown={e => {
            if (e.key === 'Enter' || e.key === ' ') {
              setDeviceId(device.deviceId);
            }
          }}
        >
          <span className="device-name">
            {truncateDeviceName(device.label || 'Default Microphone')}
          </span>
        </div>
      ),
    }));
  }

  return (
    <Dropdown
      menu={{ items }}
      trigger={['click']}
      placement="bottom"
      overlayClassName={`mic-device-dropdown${open ? ' open' : ''}`}
      open={permissionsGranted ? open : false}
      onOpenChange={(visible) => {
        if (permissionsGranted) {
          setOpen(visible);
        }
      }}
      disabled={!permissionsGranted}
    >
      {React.cloneElement(children, {
        'aria-haspopup': 'listbox',
        'aria-expanded': open,
        tabIndex: permissionsGranted ? 0 : -1,
        className: `${children.props.className || ''} mic-dropdown-trigger${open ? ' open' : ''}${!permissionsGranted ? ' disabled' : ''}`.trim(),
        style: {
          ...children.props.style,
          cursor: permissionsGranted ? 'pointer' : 'not-allowed',
          opacity: permissionsGranted ? 1 : 0.5
        }
      })}
    </Dropdown>
  );
}